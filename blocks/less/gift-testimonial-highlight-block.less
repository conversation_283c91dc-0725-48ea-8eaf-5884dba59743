// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftTestimonialHighlight {
    background: @almostWhite;
    
    .contentWrapper {
        position: relative;
        overflow: hidden;
    }
    
    .slider {
        position: relative;
        
        .item {
            display: flex;
            align-items: center;
            min-height: @vw400;
            padding: @vw70 0;
            
            .quote {
                flex: 1;
                font-size: @vw52;
                line-height: @vw63;
                color: @secondaryColor;
                font-weight: 400;
                margin: 0;
                padding-right: @vw100;
                
                &:before {
                    content: '"';
                }
                
                &:after {
                    content: '"';
                }
            }
            
            .meta {
                display: flex;
                align-items: center;
                flex-shrink: 0;
                
                .photo {
                    width: @vw200;
                    height: @vw200;
                    .rounded(50%);
                    overflow: hidden;
                    margin-right: @vw40;
                    flex-shrink: 0;
                    
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                    }
                }
                
                div {
                    color: @secondaryColor;
                    font-size: @vw21;
                    line-height: @vw32;
                    
                    strong {
                        font-weight: 600;
                        display: block;
                        margin-bottom: @vw8;
                    }
                }
            }
        }
    }
    
    // Responsive styles
    @media (max-width: 1080px) {
        .slider {
            .item {
                flex-direction: column;
                text-align: center;
                min-height: auto;
                padding: @vw50-1080 0;
                
                .quote {
                    padding-right: 0;
                    margin-bottom: @vw40-1080;
                    font-size: @vw45-1080;
                    line-height: @vw55-1080;
                }
                
                .meta {
                    flex-direction: column;
                    
                    .photo {
                        margin-right: 0;
                        margin-bottom: @vw25-1080;
                        width: @vw134-1080;
                        height: @vw134-1080;
                    }
                }
            }
        }
    }
    
    @media (max-width: 580px) {
        .slider {
            .item {
                padding: @vw40-580 0;
                
                .quote {
                    font-size: @vw35-580;
                    line-height: @vw45-580;
                    margin-bottom: @vw30-580;
                }

                .meta {
                    .photo {
                        width: @vw100-580;
                        height: @vw100-580;
                        margin-bottom: @vw20-580;
                    }

                    div {
                        font-size: @vw18-580;
                        line-height: @vw28-580;
                    }
                }
            }
        }
    }
}
