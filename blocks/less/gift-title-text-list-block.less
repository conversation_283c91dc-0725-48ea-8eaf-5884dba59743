// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftTitleTextList {
    &.inview {
        .listItems {
            .listItem {
                opacity: 1;
                .transform(translateY(0));
                transition: opacity .45s, transform .45s;
                -webkit-transition: opacity .45s, transform .45s;
                .stagger(100, 0.15s, 0.9s);
            }
            .buttonWrapper {
                opacity: 1;
                .transform(translateY(0));
                transition: opacity .45s .9s, transform .45s .9s;
                -webkit-transition: opacity .45s .9s, transform .45s .9s;
            }
        }
    }

    .cols {
        display: block;
        width: calc(100% ~"+" @vw32);
        margin-left: -@vw16;
    }

    .col {
        width: calc(60% ~"-" @vw32);
        vertical-align: top;
        display: inline-block;
        margin: 0 @vw16;
        &.leftCol {
            width: calc(40% ~"-" @vw32);
            .mainContent {
                margin-bottom: @vw60;
                .bigTitle {
                    margin-bottom: @vw30;
                }

                .mainText {
                    margin-bottom: @vw40;

                    p {
                        font-size: @vw21;
                        line-height: @vw32;
                        color: @almostWhite;
                        margin-bottom: @vw16;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }

    .listItems {
        background: @hardWhite;
        .rounded(@vw16);
        padding: @vw50;
        .innerCol {
            display: inline-block;
            vertical-align: top;
            &:first-child {
                width: 60%;
                padding-right: @vw40;
            }
            &:last-child {
                width: 40%;
                .listItem {
                    &:last-child {
                        color: @primaryColor;
                        .text, p {
                            color: @primaryColor;
                        }
                    }
                }
            }
        }

        .listItem {
            margin-bottom: @vw40;
            opacity: 0;
            .transform(translateY(@vw16));

            &:last-child {
                margin-bottom: 0;
            }
            .textTitle {
                margin-bottom: @vw6;
            }
        }
    }

    .buttonWrapper {
        display: flex;
        flex-direction: row;
        gap: @vw16;
        align-items: center;
        margin-top: @vw20;
        opacity: 0;
        .transform(translateY(@vw16));
    }
}

// responsive tweaks
@media all and (max-width: 1080px) {
    .giftTitleTextList {
        .cols {
            gap: @vw60-1080;
        }

        .col {
            width: calc(50% - @vw30-1080);

            &.rightCol {
                .rounded(@vw16-1080);
                padding: @vw50-1080;
            }
        }

        .listItems {
            .innerCols {
                gap: @vw30-1080;
            }

            .listItem {
                margin-bottom: @vw40-1080;
                .transform(translateY(@vw16-1080));

                .itemTitle {
                    font-size: @vw21-1080;
                    margin-bottom: @vw16-1080;
                }

                .itemText {
                    p, li {
                        font-size: @vw16-1080;
                        line-height: @vw25-1080;
                        margin-bottom: @vw12-1080;
                    }

                    ul, ol {
                        margin-left: @vw20-1080;
                        margin-bottom: @vw12-1080;
                    }
                }
            }
        }

        .col.leftCol {
            .mainContent {
                margin-bottom: @vw60-1080;

                .bigTitle {
                    margin-bottom: @vw30-1080;
                }

                .mainText {
                    margin-bottom: @vw40-1080;

                    p {
                        font-size: @vw21-1080;
                        line-height: @vw32-1080;
                        margin-bottom: @vw16-1080;
                    }
                }
            }
        }

        .buttonWrapper {
            margin-top: @vw50-1080;

            .button {
                margin-bottom: @vw16-1080;
            }

            .buttonSubtitle {
                font-size: @vw14-1080;
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftTitleTextList {
        .cols {
            flex-direction: column;
            gap: @vw40-580;
        }

        .col {
            width: 100%;

            &.rightCol {
                .rounded(@vw16-580);
                padding: @vw40-580;
            }
        }

        .listItems {
            .innerCols {
                flex-direction: column; // 👈 innerCols stacken op mobiel
                gap: @vw20-580;
            }

            .innerCol {
                width: 100%;
            }

            .listItem {
                margin-bottom: @vw30-580;
                .transform(translateY(@vw16-580));

                .itemTitle {
                    font-size: @vw17-580;
                    margin-bottom: @vw12-580;
                }

                .itemText {
                    p, li {
                        font-size: @vw14-580;
                        line-height: @vw21-580;
                        margin-bottom: @vw10-580;
                    }

                    ul, ol {
                        margin-left: @vw16-580;
                        margin-bottom: @vw10-580;
                    }
                }
            }
        }

        .col.leftCol {
            .mainContent {
                margin-bottom: @vw40-580;

                .bigTitle {
                    margin-bottom: @vw20-580;
                }

                .mainText {
                    margin-bottom: @vw30-580;

                    p {
                        font-size: @vw17-580;
                        line-height: @vw25-580;
                        margin-bottom: @vw12-580;
                    }
                }
            }
        }

        .buttonWrapper {
            margin-top: @vw40-580;

            .button {
                margin-bottom: @vw12-580;
            }

            .buttonSubtitle {
                font-size: @vw12-580;
            }
        }
    }
}
