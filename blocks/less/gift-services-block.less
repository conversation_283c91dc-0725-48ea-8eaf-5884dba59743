// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftServicesBlock {
    .cols {
        display: flex;
        flex-wrap: wrap;
    }
    .pin-spacer {
    }
    .sectionHeader {
        position: absolute;
        top: 0;
        left: 0;
        padding-right: @vw100 + @vw30;
    }
    .col {
        position: relative;
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .button {
        margin-top: @vw30;
    }
    .service {
        display: flex;
        gap: @vw16;
        flex-direction: row;
        opacity: 0;
        .transform(translateY(@vw16));
        &.inview {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
        }
        &:not(:last-child) {
            margin-bottom: @vw50;
        }
        strong {
            font-size: @vw12;
            color: rgba(@hardBlack, .3);
            text-transform: uppercase;
            letter-spacing: .14em;
            font-weight: 300;
        }
        .innerCol {
            width: 50%;
            .rounded(@vw20);
            &:not(.imageCol) {
                background: @hardWhite;
                padding: @vw40;
            }
            &.imageCol {
                overflow: hidden;
                position: relative;
                img {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }       
        }
    }
}