// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftUSPs {
  text-align: center;
  &.inview {
    .improvement {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity .45s, transform .45s;
      -webkit-transition: opacity .45s, transform .45s;
      .stagger(100, 0.15s, 0.9s);
    }
    .stat {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity .45s, transform .45s;
      -webkit-transition: opacity .45s, transform .45s;
      .stagger(100, 0.15s, 0.9s);
    }
  }
  .normalMediumTitle {
    margin-bottom: @vw60;
  }
  .improvements {
      margin: -@vw8 0;
      display: flex;
      width: calc(100% ~"+" @vw16);
      margin-left: -@vw8;
  }
  .improvement {
      border: 1px solid @primaryColor;
      .rounded(@vw14);
      display: inline-block;
      padding: @vw16;
      vertical-align: top;
      width: calc(20% ~"-" @vw16);
      margin: @vw8;
      flex-direction: column;
      opacity: 0;
      .transform(translateY(@vw16));
      .normalTitle {
          margin-bottom: @vw50;
          font-size: @vw32;
          line-height: 1.1;
      }
      .text {
          margin: 0;
      }
      .button {
          margin-top: @vw16;
      }
  }
  img {
    height: @vw90;
    width: auto;
    margin: auto;
    margin-bottom: @vw26;
    display: block;
  }
  .stats {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: @vw60;
    margin-top: @vw80;
    margin-bottom: @vw40;
  }
  .stat {
    flex: 1 1 0;
    max-width: 33%;
    opacity: 0;
    .transform(translateY(@vw16));
    .bigTitle { color: @primaryColor; }
    .text { margin-top: @vw12; }
  }

  .cta { margin-top: @vw30; }
}

@media all and (max-width: 1080px) {
  .giftUSPs {
    .improvement { width: calc(33.333% ~"-" @vw16-1080); min-width: auto; padding: @vw16-1080; }
    .stats { gap: @vw30-1080; }
  }
}

@media all and (max-width: 580px) {
  .giftUSPs {
    .improvements { gap: @vw12-580; }
    .improvement { width: calc(50% ~"-" @vw12-580); }
    .stat { max-width: 100%; }
    .stats { flex-direction: column; gap: @vw30-580; }
  }
}

