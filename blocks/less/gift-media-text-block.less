// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftMediaTextBlock {
    &.smaller {
        &.noMarginBottom {
            margin-bottom: @vw16 !important;
        }
        &:has(.bigButton:hover){
            .media {
                .innerWrapper {
                    .transform(scale(1.05));
                }
            }
        }
        .cols {
            .media {
                .innerWrapper {
                    .transitionMore(transform, .3s);
                    .paddingRatio(590, 604);
                    img, video {
                        height: 120%;
                    }
                }
            }
            .info {
                padding: @vw50;
                padding-bottom: @vw50 + @vw100;
            }
        }
        &.dark {
            .cols {
                align-items: inherit;
                .info {
                    height: auto;
                    background: @almostWhite;
                    position: relative;
                    overflow: hidden;
                    .rounded(@vw16);
                    .normalTitle {
                        color: @secondaryColor;
                    }
                    .text p, .text li {
                        color: @secondaryColor;
                    }
                }
            }
        }
    }
    &.side-right {
        .cols {
            flex-direction: row-reverse;
        }
    }
    .cols {
        align-items: center;
        display: flex;
        gap: @vw16;
        flex-direction: row;
        &.side-right {
            flex-direction: row-reverse;
        }
        p {
            color: rgba(@hardBlack, .7);
        }
        .media {
            .rounded(@vw14);
            width: 50%;
            overflow: hidden;
            position: relative;
            display: inline-block;
            vertical-align: top;
            height: auto;
            .innerWrapper {
                overflow: hidden;
                position: relative;
                width: 100%;
                .paddingRatio(791,889);
                height: 0;
            }
            img, video {
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
        .info {
            width: 50%;
            display: inline-block;
            vertical-align: top;
            padding: 0 @vw100 0 @vw100 + @vw20;
        }
        .normalTitle, .mediumTitle {
            margin-bottom: @vw30;
        }
        .button {
            margin-top: @vw50;
        }
    }
}