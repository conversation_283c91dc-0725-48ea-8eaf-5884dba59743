// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';
 
.giftEventPromoBlock {
    background-color: #0f1a2c;
    padding: @vw80 0;
    &.inview {
        .promoCard {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            .stagger(100, 0.15s, 0s);
        }
    }
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 @vw20;
    }
    
    .eventPromoGrid {
        display: flex;
        flex-wrap: wrap;
        margin-left: -@vw8;
        width: calc(100% ~"+" @vw16);
    }
    
    .promoCard {
        width: calc(33.3333% ~"-" @vw16);
        margin: @vw8;
        opacity: 0;
        .transform(translateY(@vw20));
        background: #fff;
        color: @secondaryColor;
        .rounded(@vw16);
        padding: @vw40;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        display: inline-block;
        vertical-align: top;
        position: relative;
        overflow: hidden;
        .transition(transform, 0.3s);
        &:nth-child(2), &:nth-child(3) {
            width: calc(66.6666% ~"-" @vw32);
        }
        &:nth-child(3) {
            min-height: @vw100 * 4.5;
        }
    }
    
    // Intro Card (links boven)
    .introCard {
        .cardTitle {
            font-size: @vw48;
            font-weight: 700;
            line-height: 1.2;
            color: @secondaryColor;
            margin-bottom: @vw30;
            
            @media (max-width: 768px) {
                font-size: @vw36;
            }
        }
    }
    
    // Beschrijving Card (rechts boven)
    .descriptionCard {
        .cardIntro {
            margin-bottom: @vw25;
            
            p {
                color: @secondaryColor;
                font-size: @vw16;
                line-height: 1.6;
                margin-bottom: @vw15;
            }
        }
        
        .bulletList {
            list-style: none;
            padding: 0;
            margin: 0 0 @vw25 0;
            
            li {
                position: relative;
                padding-left: @vw20;
                margin-bottom: @vw10;
                color: @secondaryColor;
                font-size: @vw14;
                line-height: 1.5;
                
                &:before {
                    content: "•";
                    color: #c7a56f;
                    font-weight: bold;
                    position: absolute;
                    left: 0;
                }
            }
        }
        
        .bottomText {
            font-weight: 600;
            color: #c7a56f;
            font-size: @vw14;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
    
    // Video Card (links onder)
    .videoCard {
        background: #000;
        padding: 0;
        
        .videoWrapper {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .videoContainer {
            position: relative;
            flex: 1;
            cursor: pointer;
            
            .videoThumbnail {
                position: relative;
                width: 100%;
                height: 100%;
                
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
            
            .playButton {
                cursor: pointer;
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                .transitionMore(opacity, .3s);
                &:hover {
                    opacity: .5;
                }
                * {
                    cursor: pointer;
                }
                svg {
                    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
                    path {
                        fill: @primaryColor !important;
                        &:last-child {
                            fill: #fff !important;
                        }
                    }
                }
            }
        }
        
        .videoOverlay {
            position: absolute;
            top: @vw20;
            left: @vw20;
            background: rgba(199, 165, 111, 0.9);
            color: #fff;
            padding: @vw8 @vw16;
            .rounded(@vw8);
            font-weight: 700;
            font-size: @vw14;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .videoTitle {
            position: absolute;
            bottom: @vw20;
            left: @vw20;
            right: @vw20;
            color: #fff;
            font-size: @vw24;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            
            @media (max-width: 768px) {
                font-size: @vw20;
            }
        }
    }
    
    // Event Details Card (rechts onder)
    .eventDetailsCard {
        .eventTitle {
            font-size: @vw32;
            font-weight: 700;
            color: @secondaryColor;
            margin-bottom: @vw25;
            
            @media (max-width: 768px) {
                font-size: @vw28;
            }
        }
        
        .eventMeta {
            margin-bottom: @vw25;
            
            .metaItem {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: @vw8;
                padding: @vw8 0;
                border-bottom: 1px solid #f0f0f0;
                
                &:last-child {
                    border-bottom: none;
                }
                
                .metaLabel {
                    font-size: @vw12;
                    color: #666;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                
                .metaValue {
                    font-size: @vw14;
                    font-weight: 600;
                    color: @secondaryColor;
                }
            }
        }
        
        .extraTitle {
            font-size: @vw18;
            font-weight: 600;
            color: @secondaryColor;
            margin-bottom: @vw15;
        }
        
        .extraBody {
            margin-bottom: @vw25;
            
            p {
                color: @secondaryColor;
                font-size: @vw14;
                line-height: 1.6;
                margin-bottom: @vw10;
            }
        }
    }
    
    // CTA Button styling
    .ctaButton {
        display: inline-block;
        background: #c7a56f;
        color: #fff;
        padding: @vw12 @vw30;
        .rounded(25px);
        text-decoration: none;
        font-weight: 600;
        font-size: @vw14;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        .transition(all, 0.3s);
        
        &:hover {
            background: darken(#c7a56f, 10%);
            .transform(translateY(-2px));
            box-shadow: 0 5px 15px rgba(199, 165, 111, 0.4);
        }
    }
    
    // Animation states
    [data-animate="fade-in-up"] {
        opacity: 0;
        .transform(translateY(30px));
        .transition(all, 0.6s);
        
        &.animated {
            opacity: 1;
            .transform(translateY(0));
        }
    }
}
