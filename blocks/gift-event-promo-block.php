<?php
// Get all field values
$intro_title = get_field('intro_title');
$intro_cta_text = get_field('intro_cta_text');
$intro_cta_link = get_field('intro_cta_link');

$description_intro = get_field('description_intro');
$description_bullets = get_field('description_bullets');
$description_bottom_text = get_field('description_bottom_text');

$video_url = get_field('video_url');
$video_overlay_text = get_field('video_overlay_text');
$video_title_quote = get_field('video_title_quote');

$event_title = get_field('event_title');
$event_extra_title = get_field('event_extra_title');
$event_extra_body = get_field('event_extra_body');
$event_date = get_field('event_date');
$event_location = get_field('event_location');
$event_spots = get_field('event_spots');
$event_status = get_field('event_status');
$event_cta_text = get_field('event_cta_text');
$event_cta_link = get_field('event_cta_link');


?>

<section class="giftEventPromoBlock dark" data-init>
    <div class="contentWrapper">
        <div class="eventPromoGrid">
            <!-- Intro Card (links boven) -->
            <div class="promoCard introCard" data-animate="fade-in-up">
                <?php if ($intro_title): ?>
                    <h2 class="mediumTitle" data-lines data-words><?= nl2br(esc_html($intro_title)) ?></h2>
                <?php endif; ?>
                
                <?php if ($intro_cta_text && $intro_cta_link): ?>
                    <div class="cardCta">
                        <a href="<?= esc_url($intro_cta_link) ?>" class="ctaButton">
                            <?= esc_html($intro_cta_text) ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Beschrijving Card (rechts boven) -->
            <div class="promoCard descriptionCard" data-animate="fade-in-up">
                <?php if ($description_intro): ?>
                    <div class="cardIntro">
                        <?= $description_intro ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($description_bullets): ?>
                    <ul class="bulletList">
                        <?php foreach ($description_bullets as $bullet): ?>
                            <?php if (!empty($bullet['bullet_text'])): ?>
                                <li><?= esc_html($bullet['bullet_text']) ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <?php if ($description_bottom_text): ?>
                    <div class="bottomText">
                        <?= esc_html($description_bottom_text) ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Video Card (links onder) -->
            <div class="promoCard videoCard" data-animate="fade-in-up">
                <?php
                
                // Extract YouTube video ID from URL
function get_youtube_id($url) {
    if (empty($url)) return '';
    
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : '';
}

$youtube_id = get_youtube_id($video_url);
                if ($youtube_id):
                     ?>
                    <div class="videoWrapper">
                        <div class="videoContainer" data-video-id="<?= esc_attr($youtube_id) ?>">
                            <div class="videoThumbnail">
                                <img src="https://img.youtube.com/vi/<?= esc_attr($youtube_id) ?>/maxresdefault.jpg" alt="Video thumbnail">
                                <div class="playButton">
                                    <svg width="68" height="48" viewBox="0 0 68 48">
                                        <path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path>
                                        <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($video_overlay_text): ?>
                            <div class="videoOverlay">
                                <?= esc_html($video_overlay_text) ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($video_title_quote): ?>
                            <div class="videoTitle">
                                <?= esc_html($video_title_quote) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Event Details Card (rechts onder) -->
            <div class="promoCard eventDetailsCard" data-animate="fade-in-up">
                <?php if ($event_title): ?>
                    <h3 class="eventTitle"><?= esc_html($event_title) ?></h3>
                <?php endif; ?>
                
                <div class="eventMeta">
                    <?php if ($event_date): ?>
                        <div class="metaItem">
                            <span class="metaLabel">Next Event:</span>
                            <span class="metaValue"><?= esc_html(date('d.m.y', strtotime($event_date))) ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($event_location): ?>
                        <div class="metaItem">
                            <span class="metaValue"><?= esc_html($event_location) ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($event_spots): ?>
                        <div class="metaItem">
                            <span class="metaLabel">Limited:</span>
                            <span class="metaValue"><?= esc_html($event_spots) ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($event_status): ?>
                        <div class="metaItem">
                            <span class="metaLabel">Previous Events:</span>
                            <span class="metaValue"><?= esc_html($event_status) ?></span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($event_extra_title): ?>
                    <h4 class="extraTitle"><?= esc_html($event_extra_title) ?></h4>
                <?php endif; ?>
                
                <?php if ($event_extra_body): ?>
                    <div class="extraBody">
                        <?= wpautop($event_extra_body) ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($event_cta_text && $event_cta_link): ?>
                    <div class="cardCta">
                        <a href="<?= esc_url($event_cta_link) ?>" class="ctaButton">
                            <?= esc_html($event_cta_text) ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            
        </div>
    </div>
</section>
