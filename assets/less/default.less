// out: false
@import 'vw_values.less';
@import 'constants.less';

* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0;
  padding:0;
  position:relative;
  &::selection {
    background: @primaryColor;
    color: @almostWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @almostWhite;
  }
  &:focus {
    outline: none;
  }
}


html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

html {
  overflow-x: hidden;
}
body {
  background: @almostWhite;
  color: @almostBlack;
  font-family: "Figtree", sans-serif;
  font-weight: 400;
  font-style: normal;
  overflow: hidden;
  line-height: 1;
  font-size: @vw16;
  width: 100vw;
  overflow-x: hidden;
  strong, em {
    font-weight: 700;
  }
  p {
    line-height: 1.4;
    a {
      cursor: pointer;
      color: @almostWhite;
      text-decoration: none;
      font-weight: 700;
      .transition(.15s);
      &:hover {
        opacity: .6;
      }
    }
  }
  ul {
    line-height: 1.4;
    font-family: "Figtree", sans-serif;
  }
}

img {
  pointer-events: none;
}

// Swup


.pageTransition {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: @primaryColor;
  visibility: hidden;
  clip-path: circle(50% at 50% 200%);
  -webkit-clip-path: circle(50% at 50% 170%);
  pointer-events: none;
  &.active {
    pointer-events: all;
    visibility: visible;
    z-index: 9999;
  }
}

body.touch {
  a,
  .button {
    &:hover {
      opacity: 1 !important;
      color: inherit !important;
      background: inherit !important;
      transform: none !important;
      transition: none !important;
    }
  }
  .button {
    &:hover {
      background: @primaryColor !important;
    }
  }
}

[data-scroll-section] {
  background: @almostWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

section {
  margin: @vw100 + @vw100 + @vw20 0;
  padding: 0 @vw8;
  &:first-of-type {
    margin-top: 0;
    padding-top: @vw100 * 2;
  }
  &.white {
    padding: @vw100 0;
    border-radius: @vw30; 
    color: @hardBlack;
    background: #FFFFFF;
  }
  &.grey { /* default look */ }
  &.dark {
    // margin: 0;
    color: @almostWhite;
    &:before {
      content: '';
      position: absolute;
      top: -(@vw100 * 2.2);
      left: 0;
      width: 100%;
      height: calc(100% ~"+" @vw100 * 4.4);
      background: @secondaryColor;
      z-index: -2;
    }
    p {
      color: rgba(243, 241, 237, .7);
    }
  }
  &.paddingTop {
    padding-top: @vw100 * 2.2;
  }
  &.noMarginTop {
    margin-top: 0 !important;
    &.white {
      padding-top: @vw20;
    }
  }
  &.noMarginBottom {
    margin-bottom: 0 !important;
    &.white {
      padding-bottom: @vw20;
    }
  }
  .staggerWordsChild(@i, @transition, @delay: 0s) when (@i > 0) {
    &:nth-child(@{i}) {
        .word {
          transition-delay: (@i * @transition + @delay);
        }
    }
    .staggerWordsChild(@i - 1, @transition, @delay);
}
  &.inview {
    [data-lines] {
      visibility: visible;
      .line {
        .staggerWordsChild(100, 0.15s, 0.4s);
        .word, .letter {
          .transform(translateY(0));
          .transitionMore(transform, .75s, 0s, cubic-bezier(0, 0.55, 0.45, 1));
        }
      }
    }
  }
  [data-lines] {
    .line {
      position: relative;
      overflow: hidden;
      .word, .letter {
        .transform(translateY(100%));
        will-change: transform;
      }
    }
  }
}

[data-lines] {
  visibility: hidden;
}

.contentWrapper {
  display: block;
  width: 100%;
  padding: 0 @vw100 + @vw60;
  &.smaller {
    padding: 0 @vw104 + @vw112 + @vw16;
  }
  &.smallest {
    padding: 0 @vw104 + @vw112 + @vw16 + @vw112 + @vw16;
  }
}

.noise {
  background-color: rgba(20,20,20,.3);
  background-position: 0;
  height: 100%;
  opacity: 1;
  pointer-events: none;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
}

[data-show-mouse] {
  cursor: none !important;
  * {
    cursor: none !important;
  }
}

@keyframes noise{
  0%{
      background-position: 0 0;
  }
  100%{
      background-position: 100% 100%;
  }
}

.textLink {
  display: inline-table;
  cursor: pointer;
  font-size: @vw16;
  line-height: 1.4;
  font-family: "Figtree", sans-serif;
  font-weight: 400;
  font-style: normal;
  color: @almostWhite;
  text-transform: uppercase;
  text-decoration: none;
  .transitionMore(color,.3s);
  &.bigger {
    font-size: @vw50;
    .innerText {
      display: inline;
      padding-right: @vw30;
      width: calc(100% - @vw64);
    }
    .arrows {
      width: @vw50;
      height: @vw50;
      font-size: @vw30;
      line-height: @vw50;
    }
  }
  * {
    cursor: pointer;
  }
  &:hover {
    color: @primaryColor;
    .arrows {
      border-color: @primaryColor;
      i {
        &:first-child {
          .transform(translate(-50%, -50%));
        }
        &:last-child {
          .transform(translate(50%, -150%) scale(.5));
        }
      }
    }
  }
  .innerText {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - @vw34);
    padding-right: @vw16;
  }
  .arrows {
    display: inline-block;
    vertical-align: middle;
    width: @vw34;
    height: @vw34;
    border: 1px solid @almostWhite;
    line-height: @vw34;
    .rounded(50%);
    text-align: center;
    position: relative;
    overflow: hidden;
    .transitionMore(border-color,.3s);
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      .transform(translate(-50%, -50%));
      .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
      &:first-child {
        .transform(translate(-150%, 50%) scale(.5));
      }
    }
  }
}

.arrowButton {
  cursor: pointer;
  display: inline-block;
  font-size: @vw32;
  .rounded(@vw16);
  text-align: center;
  line-height: @vw63;
  width: @vw63;
  height: @vw63;
  overflow: hidden;
  font-size: @vw20;
  color: @primaryColor;
  position: relative;
  vertical-align: middle;
  transition: color .3s, border .3s, border-radius .3s;
  -webkit-transition: color .3s, border .3s, border-radius .3s;
  &.prev {
    &:hover {
      i {
        &:last-child {
          .transform(translate(-250%, -50%) scale(.5));
        }
      }
    }
    i {
      &:first-child {
        .transform(translate(250%, -50%) scale(.5));
      }
    }
  }
  &:hover {
    color: @primaryColor;
    .rounded(@vw63);
    i {
      &:first-child {
        .transform(translate(-50%, -50%));
      }
      &:last-child {
        .transform(translate(250%, -50%) scale(.5));
      }
    }
  }
  i {
    cursor: pointer;
    position: absolute;
    left: 50%;
    top: 50%;
    .transform(translate(-50%, -50%));
    .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
    &:first-child {
      .transform(translate(-250%, -50%) scale(.5));
    }
  }
}

#background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.dg.ac {
  z-index: 3 !important;
}

@media all and (max-width: 1080px) {
  body {
    font-size: @vw18-1080;
  }
  section {
    margin: @vw100-1080 + @vw80-1080 0;
    padding: 0 @vw8-1080;
    &:first-of-type {
      padding-top: @vw100-1080 * 2;
    }
    &.white {
      padding: @vw100-1080 0;
      border-radius: @vw30-1080;
    }
  }
  .contentWrapper {
    padding: 0 @vw40-1080;
    &.smaller {
      padding: 0 @vw40-1080 + @vw32-1080;
    }
    &.smallest {
      padding: 0 @vw40-1080 + @vw32-1080;
    }
  }
  .textLink {
    font-size: @vw14-1080;
    .innerText {
      width: calc(100% - @vw34-1080);
      padding-right: @vw16-1080;
    }
    &.bigger {
      font-size: @vw50-1080;
      .innerText {
        padding-right: @vw30-1080;
        width: calc(100% - @vw64-1080);
      }
      .arrows {
        width: @vw50-1080;
        height: @vw50-1080;
        font-size: @vw30-1080;
        line-height: @vw50-1080;
      }
    }
    .arrows {
      width: @vw34-1080;
      height: @vw34-1080;
      line-height: @vw34-1080;
    }
  }
  .arrowButton {
    font-size: @vw16-1080;
    line-height: @vw50-1080;
    width: @vw50-1080;
    height: @vw50-1080;
  }
}

@media all and (max-width: 580px) {
  body {
    font-size: @vw16-580;
    line-height: 1.2;
  }
  .lines {
    .line {
      &:nth-child(1), &:nth-child(2) {
        display: none;
      }
    }
  }
  section {
    margin: @vw100-580 + @vw70-580 0;
    padding: 0 @vw16-580;
    &:first-of-type {
      padding-top: @vw100-580 * 2.5;
    }
    &.white {
      padding: @vw100-580 0;
      border-radius: @vw30-580;
    }
  }
  .contentWrapper {
    padding: 0 @vw16-580;
    &.smaller {
      padding: 0 @vw16-580;
    }
    &.smallest {
      padding: 0 @vw16-580;
    }
  }
  .textLink {
    font-size: @vw24-580;
    &.bigger {
      font-size: @vw24-580;
      .innerText {
        width: calc(100% - @vw34-580);
        padding-right: @vw16-580;
      }
      .arrows {
        width: @vw34-580;
        height: @vw34-580;
        line-height: @vw34-580;
      }
    }
    .innerText {
      width: calc(100% - @vw34-580);
      padding-right: @vw16-580;
    }
    .arrows {
      width: @vw34-580;
      height: @vw34-580;
      line-height: @vw34-580;
    }
  }
  .arrowButton {
    font-size: @vw32-580;
    line-height: @vw63-580;
    width: @vw63-580;
    height: @vw63-580;
  }
}