// out: false
@import '../vw_values.less';
@import '../constants.less';

html {
    &:not(.touch) {
        .button {
            &:hover {
              &:before{
                .transform(scaleY(1));
              }
              .innerText {
                .word {
                  .stagger(100, 0.08s);
                }
                &:first-child {
                  .word {
                    .transform(translateY(-50%));
                    opacity: 0;
                  }
                }
                &:last-child {
                  .word {
                    .transform(translateY(0));
                    opacity: 1;
                  }
                }
              }
            }
        }
    }
}
.button {
  .rounded(@vw100);
  border: 1px solid @primaryColor;
  display: inline-block;
  background: transparent;
  cursor: pointer;
  color: @almostWhite;
  font-family: "Figtree", sans-serif;
  font-weight: 300;
  font-size: @vw16;
  font-style: normal;
  text-align: center;
  height: @vw44;
  line-height: @vw44;
  padding: 0 @vw44;
  color: @almostWhite;
  text-decoration: none;
  position: relative;
  text-transform: uppercase;
  overflow: hidden;
  &.primary {
    border-color: @primaryColor;
    background: @primaryColor;
    color: @almostWhite;
    &:before { 
      background: @secondaryColor;
    }
  }
  &.secondary {
    border-color: @secondaryColor;
    background: @secondaryColor;
    color: @almostWhite;
    &:before { 
      background: @primaryColor;
      border-color: @primaryColor;
    }
  }
  &.whiteText {
    color: @almostWhite;
  }
  &.outline {
    border-color: @secondaryColor;
    color: @secondaryColor;
    &:hover {
      color: @almostWhite;
    }
    &:before { 
      background: @secondaryColor;
    }
  }
  .word {
    letter-spacing: .14em;
    transition: opacity .3s ease-in-out, transform .3s ease-in-out;
    -webkit-transition: opacity .3s ease-in-out, transform .3s ease-in-out;
  }
  * {
    cursor: pointer;
  }
  &:before {
    content: '';
    position: absolute;
    top: 0;
    height: 100%;
    left: 0;
    width: 100%;
    transform-origin: top;
    .transform(scaleY(0));
    background: @primaryColor;
    .transitionMore(transform, .3s);
  }
  .innerTexts {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    left: 0;
    .transition(transform, .3s);
  }
  .hiddenText {
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
  }
  .innerText {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    line-height: inherit;
    display: block;
    pointer-events: none;
    &:first-child {
      .word {
        .transform(translateY(0));
      }
    }
    &:last-child {
      .word {
        opacity: 0;
        .transform(translateY(50%));
      }
    }
  }
}

.bigButton {
    color: @hardWhite;
    cursor: pointer;
    background: @primaryColor;
    padding: @vw35;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-decoration: none;
    .transitionMore(filter, .3s);
    &:hover {
        filter: brightness(.8);
        -webkit-filter: brightness(.8);
        .textTitle {
            color: @hardWhite;
        }
    }
    .textTitle {
        cursor: pointer;
        color: rgba(@hardWhite, .5);
        margin-bottom: @vw10;
        .transitionMore(color, .3s);
    }
}

.socialLink {
    display: inline-block;
    background: transparent;
    height: @vw50;
    width: @vw50;
    line-height: @vw55;
    color: rgba(@primaryColor, .7);
    text-decoration: none;
    text-align: center;
    font-size: @vw26;
    .rounded(50%);
    cursor: pointer;
    border: 1px solid @primaryColor;
    transition: background .3s, color .3s;
    -webkit-transition: background .3s, color .3s;
    &:hover {
      background: rgba(@primaryColor, 1);
      color: rgba(@hardWhite, 1);
    }
    * {
      cursor: pointer;
    }
}

@media all and (max-width: 1080px) {
  html {
    &:not(.touch) {
        .button {
            &:hover {
                padding-left: @vw68-1080;
            }
        }
    }
  }
  .button {
    font-size: @vw16-1080;
    padding-right: @vw68-1080;
    .body {
      white-space: nowrap;
      .rounded(@vw100-1080);
      height: @vw68-1080;
      line-height: @vw68-1080;
      padding: 0 @vw26-1080;
    }
    .ball {
      width: @vw68-1080;
      height: @vw68-1080;
      i {
        font-size: @vw26-1080;
      }
    }
  }

  .socialLink {
      height: @vw50-1080;
      width: @vw50-1080;
      line-height: @vw55-1080;
      font-size: @vw26-1080;
  }
}

@media all and (max-width: 580px) {
  html {
    &:not(.touch) {
        .button {
            &:hover {
                padding-left: @vw68-580;
            }
        }
    }
  }
  .button {
    font-size: @vw16-580;
    padding-right: @vw68-580;
    .body {
      white-space: nowrap;
      .rounded(@vw100-580);
      height: @vw68-580;
      line-height: @vw68-580;
      padding: 0 @vw26-580;
    }
    .ball {
      width: @vw68-580;
      height: @vw68-580;
      i {
        font-size: @vw26-580;
      }
    }
  }

  .socialLink {
      background: @primaryColor;
      color: @hardWhite;
      height: @vw68-580;
      width: @vw68-580;
      line-height: @vw70-580;
      font-size: @vw30-580;
  }
}